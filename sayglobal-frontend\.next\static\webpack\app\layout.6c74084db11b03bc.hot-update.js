"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbaaffa821b9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXERlc2t0b3BcXFNheWdsb2JhbFxcc2F5Z2xvYmFsLWZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmYmFhZmZhODIxYjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addressService: () => (/* binding */ addressService),\n/* harmony export */   cartService: () => (/* binding */ cartService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   productService: () => (/* binding */ productService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var axios_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios-retry */ \"(app-pages-browser)/./node_modules/axios-retry/dist/esm/index.js\");\n/* harmony import */ var _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants/apiEndpoints */ \"(app-pages-browser)/./src/constants/apiEndpoints.ts\");\n/* harmony import */ var _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/networkStore */ \"(app-pages-browser)/./src/stores/networkStore.ts\");\n\n\n\n\n// API Base URL - Development'te proxy kullan, production'da direkt API\nconst API_BASE_URL =  true ? '' // Development'te Next.js proxy kullanır\n : 0;\n// Cookie'den belirli bir değeri okuma utility fonksiyonu\nconst getCookieValue = (name)=>{\n    if (typeof document === 'undefined') return null; // SSR kontrolü\n    const value = \"; \".concat(document.cookie);\n    const parts = value.split(\"; \".concat(name, \"=\"));\n    if (parts.length === 2) {\n        var _parts_pop;\n        const cookieValue = (_parts_pop = parts.pop()) === null || _parts_pop === void 0 ? void 0 : _parts_pop.split(';').shift();\n        return cookieValue || null;\n    }\n    return null;\n};\n// Axios instance oluştur - HTTP-only cookies için withCredentials: true\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    },\n    timeout: 30000,\n    withCredentials: true\n});\n// Yeniden deneme mekanizmasını yapılandır\n(0,axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(apiClient, {\n    retries: 3,\n    retryCondition: (error)=>{\n        var _error_response;\n        // Sadece ağ hatalarında veya sunucu tarafı geçici hatalarında yeniden dene.\n        // 401/403 gibi client hatalarında yeniden deneme, çünkü bu auth akışını geciktirir.\n        if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) && error.response.status >= 400 && error.response.status < 500) {\n            return false;\n        }\n        return axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isNetworkError(error) || axios_retry__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isIdempotentRequestError(error);\n    },\n    retryDelay: (retryCount, error)=>{\n        console.warn(\"[axios-retry] Request failed: \".concat(error.message, \". Retry attempt #\").concat(retryCount, \"...\"));\n        // Her denemede bekleme süresini artır (1s, 2s, 4s)\n        return Math.pow(2, retryCount - 1) * 1000;\n    }\n});\n// Refresh işlemi devam ediyor mu kontrolü\nlet isRefreshing = false;\nlet failedQueue = [];\nconst processQueue = function(error) {\n    let token = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    failedQueue.forEach((param)=>{\n        let { resolve, reject } = param;\n        if (error) {\n            reject(error);\n        } else {\n            resolve(token);\n        }\n    });\n    failedQueue = [];\n};\n// Herkese açık ve engellenmemesi gereken endpoint'ler\nconst publicEndpoints = [\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REGISTER,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN,\n    _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGOUT\n];\n// Request interceptor - Cookie'leri otomatik gönder (HttpOnly cookie'ler için)\napiClient.interceptors.request.use((config)=>{\n    var _config_method;\n    console.log('📡 API Request başlıyor:', (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(), config.url);\n    console.log('🍪 withCredentials:', config.withCredentials);\n    // HttpOnly cookie'ler JavaScript ile okunamaz, bu normal bir durum\n    // withCredentials: true olduğu için browser cookie'leri otomatik gönderir\n    // Backend HttpOnly cookie'yi kontrol edecek\n    // Eğer cookie JavaScript ile okunabiliyorsa Authorization header'ına da ekle\n    const accessToken = getCookieValue('AccessToken');\n    if (accessToken) {\n        config.headers.Authorization = \"Bearer \".concat(accessToken);\n        console.log('🔑 Authorization header eklendi (JS readable cookie)');\n    } else {\n        console.log('🔑 Cookie HttpOnly olabilir - browser otomatik gönderecek');\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor - Token yenileme ve hata yönetimi\napiClient.interceptors.response.use((response)=>{\n    // Başarılı response'larda network status'u online'a çek\n    const currentStatus = _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().status;\n    if (currentStatus !== 'online') {\n        console.log('✅ API başarılı - Network status online\\'a çekiliyor');\n        _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('online');\n    }\n    return response;\n}, async (error)=>{\n    var _originalRequest_url, _originalRequest_url1, _error_response, _error_response1;\n    const originalRequest = error.config;\n    // Login endpoint'inden gelen 401 hatasını token refresh döngüsünden çıkar\n    // User/Me endpoint'ini bu kontrolden kaldırıyoruz ki token süresi dolduğunda yenileyebilsin.\n    // REFRESH endpoint'ini de döngüden çıkarıyoruz ki sonsuz döngüye girmesin.\n    if ((((_originalRequest_url = originalRequest.url) === null || _originalRequest_url === void 0 ? void 0 : _originalRequest_url.includes(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.LOGIN)) || ((_originalRequest_url1 = originalRequest.url) === null || _originalRequest_url1 === void 0 ? void 0 : _originalRequest_url1.includes(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN))) && ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        return Promise.reject(error);\n    }\n    // HİBRİT ÇÖZÜM - HttpOnly Cookie'ler için Güncellenmiş Yaklaşım:\n    // HttpOnly cookie'ler JavaScript ile okunamaz, bu yüzden cookie varlığını kontrol edemeyiz.\n    // Bunun yerine, token yenileme isteğini gönderip sonucuna göre karar vereceğiz.\n    // Eğer refresh token yoksa, backend 401 döndürecek ve biz bunu yakalayacağız.\n    // 1. ÖNCELİK: Token yenileme mantığı\n    // 401 durumunda token yenileme - retry flag kontrolü ile döngüyü engelle\n    if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true; // Bu request'in retry edildiğini işaretle\n        if (isRefreshing) {\n            // Eğer refresh işlemi devam ediyorsa, kuyruğa ekle\n            return new Promise((resolve, reject)=>{\n                failedQueue.push({\n                    resolve,\n                    reject\n                });\n            }).then(()=>{\n                // Refresh tamamlandıktan sonra orijinal isteği tekrar gönder\n                return apiClient(originalRequest);\n            }).catch((err)=>{\n                return Promise.reject(err);\n            });\n        }\n        isRefreshing = true;\n        // Refresh token için özel retry mekanizması\n        const attemptRefresh = async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n            try {\n                const refreshResponse = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REFRESH_TOKEN);\n                return refreshResponse;\n            } catch (refreshError) {\n                var _refreshError_response;\n                // Retry koşulları:\n                // 1. Ağ hatası (timeout, connection error vb.)\n                // 2. 401 hatası ama henüz max retry'a ulaşmadıysak (timeout nedeniyle 401 olabilir)\n                const isNetworkError = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && !refreshError.response;\n                const is401Error = axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && ((_refreshError_response = refreshError.response) === null || _refreshError_response === void 0 ? void 0 : _refreshError_response.status) === 401;\n                const shouldRetry = (isNetworkError || is401Error) && retryCount < 2;\n                if (shouldRetry) {\n                    console.log(\"\\uD83D\\uDD04 Refresh token denemesi \".concat(retryCount + 1, \"/3 başarısız (\").concat(isNetworkError ? 'Ağ hatası' : '401 - Timeout olabilir', \"). \").concat(retryCount < 1 ? 'Tekrar deneniyor...' : 'Son deneme yapılıyor...'));\n                    // Exponential backoff: 1s, 2s, 4s\n                    const delay = Math.pow(2, retryCount) * 1000;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return attemptRefresh(retryCount + 1);\n                }\n                // Max retry'a ulaştık veya kesin bir hata aldık\n                throw refreshError;\n            }\n        };\n        try {\n            // Refresh token çağrısı - retry mekanizması ile\n            const refreshResponse = await attemptRefresh();\n            // Tüm bekleyen istekleri başarılı olarak işaretle\n            processQueue(null);\n            isRefreshing = false;\n            // Başarılı refresh sonrası orijinal isteği tekrar gönder\n            return apiClient(originalRequest);\n        } catch (refreshError) {\n            var _refreshError_response;\n            // ÖNEMLİ: isRefreshing bayrağını mutlaka sıfırla\n            isRefreshing = false;\n            // 3 deneme sonrasında da 401 alıyorsak, bu gerçekten kullanıcının giriş yapmadığı anlamına gelir\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && ((_refreshError_response = refreshError.response) === null || _refreshError_response === void 0 ? void 0 : _refreshError_response.status) === 401) {\n                console.log('🚪 3 deneme sonrasında da 401 hatası. Kullanıcı gerçekten giriş yapmamış.');\n                // Tüm bekleyen istekleri orijinal hata ile reddet\n                processQueue(error, null);\n                // Orijinal hatayı döndür ki uygulama \"giriş yapmamış\" durumuna geçsin\n                return Promise.reject(error);\n            }\n            // Diğer hatalar için normal işlem\n            processQueue(refreshError, null);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(refreshError) && !refreshError.response) {\n                // 3 deneme sonrasında hala ağ hatası alıyorsak, bu ciddi bir bağlantı sorunu\n                console.log('🔌 3 deneme sonrasında refresh token yenilenemedi. Ağ bağlantısı sorunlu.');\n                _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('reconnecting');\n            } else {\n                console.log('💥 Beklenmedik hata sonrası auth:force-logout event gönderiliyor');\n                window.dispatchEvent(new CustomEvent('auth:force-logout'));\n            }\n            return Promise.reject(refreshError);\n        }\n    }\n    // 2. ÖNCELİK: Genel ağ hatalarını yakalama\n    // Eğer hata 401 değilse ve bir ağ hatasıysa (sunucudan yanıt yoksa),\n    // bu genel bir internet kesintisidir.\n    if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error) && !error.response) {\n        console.log('🔌 Genel ağ hatası algılandı. Yeniden bağlanma moduna geçiliyor.');\n        _stores_networkStore__WEBPACK_IMPORTED_MODULE_2__.useNetworkStore.getState().setStatus('reconnecting');\n        // Burada hatayı yutup banner'ın çalışmasına izin veriyoruz.\n        // Component'in tekrar denemesi için hatayı reject etmiyoruz ki sürekli error state'i göstermesin.\n        // Bunun yerine, bir daha asla çözülmeyecek bir promise döndürerek isteği askıda bırakıyoruz.\n        return new Promise(()=>{});\n    }\n    // Diğer tüm hatalar (500, 404, 403 vb.) normal şekilde componente geri dönsün.\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n// ===========================================\n// ADDRESS SERVICES\n// ===========================================\nconst addressService = {\n    // Kullanıcının adreslerini listele\n    async getAddresses () {\n        try {\n            console.log('📍 Adresler alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.USER_ADDRESSES);\n            console.log('📍 API Response:', response);\n            console.log('📍 Response data:', response.data);\n            console.log('📍 Response status:', response.status);\n            console.log('📍 Response data type:', typeof response.data);\n            console.log('📍 Response data is array:', Array.isArray(response.data));\n            // Eğer response.data bir object ise, içindeki property'leri kontrol et\n            if (typeof response.data === 'object' && response.data !== null) {\n                console.log('📍 Response data keys:', Object.keys(response.data));\n                console.log('📍 Response data values:', Object.values(response.data));\n                // Muhtemel nested yapıları kontrol et\n                if (response.data.data) {\n                    console.log('📍 Nested data found:', response.data.data);\n                    console.log('📍 Nested data is array:', Array.isArray(response.data.data));\n                }\n                if (response.data.addresses) {\n                    console.log('📍 Addresses property found:', response.data.addresses);\n                    console.log('📍 Addresses is array:', Array.isArray(response.data.addresses));\n                }\n                if (response.data.result) {\n                    console.log('📍 Result property found:', response.data.result);\n                    console.log('📍 Result is array:', Array.isArray(response.data.result));\n                }\n            }\n            // Farklı response yapılarını dene\n            let addressData = response.data;\n            // Eğer response.data.data varsa ve array ise onu kullan\n            if (response.data.data && Array.isArray(response.data.data)) {\n                addressData = response.data.data;\n                console.log('📍 Using nested data array');\n            } else if (response.data.addresses && Array.isArray(response.data.addresses)) {\n                addressData = response.data.addresses;\n                console.log('📍 Using addresses property');\n            } else if (response.data.result && Array.isArray(response.data.result)) {\n                addressData = response.data.result;\n                console.log('📍 Using result property');\n            } else if (Array.isArray(response.data)) {\n                addressData = response.data;\n                console.log('📍 Using direct response data');\n            } else {\n                console.warn('📍 No valid array found in response, using empty array');\n                addressData = [];\n            }\n            console.log('📍 Final address data:', addressData);\n            console.log('📍 Final address data type:', typeof addressData);\n            console.log('📍 Final address data is array:', Array.isArray(addressData));\n            console.log('📍 Final address count:', addressData.length);\n            return {\n                success: true,\n                data: addressData\n            };\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error('❌ Adresler alınırken hata:', error);\n            console.error('❌ Error response:', error.response);\n            console.error('❌ Error data:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            return {\n                success: false,\n                error: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Adresler alınırken bir hata oluştu',\n                data: [] // Hata durumunda boş array döndür\n            };\n        }\n    },\n    // Yeni adres ekle\n    async createAddress (addressData) {\n        try {\n            console.log('➕ Yeni adres oluşturuluyor:', addressData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_ADDRESS, addressData);\n            console.log('✅ Adres oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Adres eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Adres eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Adres sil\n    async deleteAddress (addressId, userId) {\n        try {\n            console.log('🗑️ Adres siliniyor:', {\n                addressId,\n                userId\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_ADDRESS, {\n                addressId,\n                userId\n            });\n            console.log('✅ Adres silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Adres silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Adres silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Varsayılan adres ayarla\n    async setDefaultAddress (addressId) {\n        try {\n            console.log('⭐ Varsayılan adres ayarlanıyor:', {\n                addressId\n            });\n            const response = await apiClient.post(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.SET_DEFAULT_ADDRESS, \"?addressId=\").concat(addressId));\n            console.log('✅ Varsayılan adres ayarlama başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Varsayılan adres ayarlanırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Varsayılan adres ayarlanırken bir hata oluştu'\n            };\n        }\n    }\n};\n// ===========================================\n// USER SERVICES\n// ===========================================\nconst userService = {\n    // Kullanıcı profilini güncelle\n    async updateProfile (profileData) {\n        try {\n            console.log('👤 Profil güncelleniyor:', profileData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_PROFILE, profileData);\n            console.log('✅ Profil güncelleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Profil güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Profil güncellenirken bir hata oluştu'\n            };\n        }\n    },\n    // Admin kullanıcıları getir (filtreleme ve sayfalama ile)\n    async getUsers (params) {\n        try {\n            // Request body oluştur\n            const requestBody = {\n                page: params.page || 1,\n                pageSize: params.pageSize || 10,\n                search: params.search || \"\"\n            };\n            // Sadece roleId 0'dan farklıysa ekle\n            if (params.roleId && params.roleId > 0) {\n                requestBody.roleId = params.roleId;\n            }\n            // Sadece isActive parametresi gönderildiyse ekle\n            if (params.isActive !== undefined) {\n                requestBody.isActive = params.isActive;\n            }\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_USERS, requestBody);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcılar alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcılar alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kullanıcı rol sayılarını getir\n    async getUserRoleCounts () {\n        try {\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_USER_ROLE_COUNTS);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı rol sayıları alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcı rol sayıları alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Kullanıcı indirim oranını getir\n    async getDiscountRate () {\n        try {\n            console.log('💰 Kullanıcı indirim oranı alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_DISCOUNT_RATE);\n            console.log('✅ Kullanıcı indirim oranı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı indirim oranı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Kullanıcı indirim oranı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    }\n};\n// ===========================================\n// PRODUCT SERVICES\n// ===========================================\nconst productService = {\n    // Markaları getir\n    async getBrands () {\n        try {\n            console.log('🏷️ Markalar alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_BRANDS);\n            console.log('✅ Markalar başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Markalar alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Markalar alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Markaya göre kategorileri getir\n    async getCategoriesByBrand (brandId) {\n        try {\n            console.log('📂 Kategoriler alınıyor, brandId:', brandId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATEGORIES_BY_BRAND, \"/\").concat(brandId));\n            console.log('✅ Kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kategoriye göre alt kategorileri getir\n    async getSubCategories (categoryId) {\n        try {\n            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORIES, \"/\").concat(categoryId, \"/subcategories\"));\n            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Alt kategori özelliklerini getir\n    async getSubCategoryFeatures (subCategoryId) {\n        try {\n            console.log('🔧 Alt kategori özellikleri alınıyor, subCategoryId:', subCategoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORY_FEATURES, \"/\").concat(subCategoryId));\n            console.log('✅ Alt kategori özellikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategori özellikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategori özellikleri alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Özellik değerlerini getir\n    async getFeatureValues (definitionId) {\n        try {\n            console.log('🏷️ Özellik değerleri alınıyor, definitionId:', definitionId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_FEATURE_VALUES, \"/\").concat(definitionId));\n            console.log('✅ Özellik değerleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Özellik değerleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Özellik değerleri alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Tüm özellik tanımlarını getir\n    async getAllFeatureDefinitions () {\n        try {\n            console.log('🔍 Tüm özellik tanımları alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ALL_FEATURE_DEFINITIONS);\n            console.log('✅ Tüm özellik tanımları başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Tüm özellik tanımları alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Tüm özellik tanımları alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Yeni ürün ekle (tam ürün - varyantlarla birlikte)\n    async createFullProduct (productData) {\n        try {\n            console.log('➕ Tam ürün oluşturuluyor:', productData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_FULL_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Tam ürün oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Tam ürün oluşturulurken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün oluşturulurken bir hata oluştu'\n            };\n        }\n    },\n    // Satıcı ürünü ekle (dealership product - PV/CV/SP olmadan)\n    async createDealershipProduct (productData) {\n        try {\n            console.log('➕ Satıcı ürünü oluşturuluyor:', productData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CREATE_DEALERSHIP_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Satıcı ürünü oluşturma başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Satıcı ürünü oluşturulurken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün oluşturulurken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görseli ekle\n    async addProductImage (imageData) {\n        try {\n            console.log('🖼️ Ürün görseli ekleniyor:', imageData);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ADD_PRODUCT_IMAGE, imageData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Ürün görseli ekleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görselini sil\n    async deleteProductImage (imageId) {\n        try {\n            console.log('🗑️ Ürün görseli siliniyor, imageId:', imageId);\n            const response = await apiClient.delete(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_PRODUCT_IMAGE, \"/\").concat(imageId));\n            console.log('✅ Ürün görseli silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün görselini değiştir\n    async replaceProductImage (imageId, newImageFile) {\n        try {\n            console.log('🔄 Ürün görseli değiştiriliyor, imageId:', imageId);\n            const formData = new FormData();\n            formData.append('file', newImageFile);\n            const response = await apiClient.put(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REPLACE_PRODUCT_IMAGE, \"/\").concat(imageId), formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Ürün görseli değiştirme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün görseli değiştirilirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün görseli değiştirilirken bir hata oluştu'\n            };\n        }\n    },\n    // Admin ürünlerini getir\n    async getAdminProducts (params) {\n        try {\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ADMIN_PRODUCTS, {\n                params\n            });\n            return Array.isArray(response.data) ? response.data : [];\n        } catch (error) {\n            console.error('❌ Admin ürünleri alınırken hata:', error);\n            return [];\n        }\n    },\n    // Kullanıcıya ait ürünleri getir\n    async getMyProducts (params) {\n        try {\n            console.log('📦 Kullanıcıya ait ürünler alınıyor:', params);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_MY_PRODUCTS, params);\n            console.log('✅ Kullanıcıya ait ürünler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcıya ait ürünler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürünler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Dealership ürün detayını getir\n    async getDealershipProductDetail (productId) {\n        try {\n            console.log('📦 Dealership ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_DEALERSHIP_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Dealership ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Dealership ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Ürünü sil\n    async deleteProduct (productId) {\n        try {\n            console.log('🗑️ Ürün siliniyor, productId:', productId);\n            // API GET metodu ve query parametresi beklediği için ona uygun istek atıyoruz.\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.DELETE_PRODUCT, {\n                params: {\n                    productId\n                }\n            });\n            console.log('✅ Ürün silme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün silinirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün silinirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürünü güncelle (tam ürün - varyantlarla birlikte)\n    async updateFullProduct (productData) {\n        try {\n            console.log('🔄 Tam ürün güncelleniyor:', productData);\n            // FormData içeriğini detaylı logla\n            console.log('📋 FormData içeriği:');\n            for (let [key, value] of productData.entries()){\n                console.log(\"\".concat(key, \":\"), value);\n            }\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_FULL_PRODUCT, productData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            console.log('✅ Tam ürün güncelleme başarılı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response1, _error_response_data, _error_response2, _error_response_data1, _error_response3;\n            console.error('❌ Tam ürün güncellenirken hata:', error);\n            console.error('❌ Hata detayları:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error('❌ HTTP Status:', (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            // Hata mesajını daha detaylı döndür\n            const errorMessage = ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data1 = _error_response3.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.title) || error.message || 'Ürün güncellenirken bir hata oluştu';\n            throw new Error(errorMessage);\n        }\n    },\n    // Admin ürün istatistiklerini getir\n    async getAdminProductStatistics () {\n        try {\n            console.log('📊 Admin ürün istatistikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_ADMIN_PRODUCT_STATISTICS);\n            console.log('✅ Admin ürün istatistikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Admin ürün istatistikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'İstatistikler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Kullanıcı ürün istatistiklerini getir\n    async getMyProductStatistics () {\n        try {\n            console.log('📊 Kullanıcı ürün istatistikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_MY_PRODUCT_STATISTICS);\n            console.log('✅ Kullanıcı ürün istatistikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data.data // API response'u data wrapper'ı içinde geliyor\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kullanıcı ürün istatistikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'İstatistikler alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Basit ürün güncelleme (dealership için)\n    async updateSimpleProduct (productFormData) {\n        console.log('🔄 API Service: Basit ürün güncelleniyor...');\n        console.log('🔗 Endpoint:', _constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT);\n        // FormData içeriğini logla\n        console.log('📋 API Service: FormData contents:');\n        for (let [key, value] of productFormData.entries()){\n            if (value instanceof File) {\n                console.log(\"  \".concat(key, \": File(\").concat(value.name, \", \").concat(value.size, \" bytes, \").concat(value.type, \")\"));\n            } else {\n                console.log(\"  \".concat(key, \": \").concat(value));\n            }\n        }\n        const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT, productFormData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        console.log('✅ API Service: Basit ürün başarıyla güncellendi');\n        console.log('📄 Response status:', response.status);\n        console.log('📄 Response data:', response.data);\n        return response.data;\n    },\n    // Ürün detayını getir\n    async getProductDetail (productId) {\n        try {\n            console.log('📦 Ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Catalog ürün detayını getir (public)\n    async getCatalogProductDetail (productId) {\n        try {\n            console.log('📦 Catalog ürün detayı alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATALOG_PRODUCT_DETAIL, \"/\").concat(productId));\n            console.log('✅ Catalog ürün detayı başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Catalog ürün detayı alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün detayı alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Ürün durumunu güncelle (onay/red)\n    async updateProductStatus (productId, isApproved, message) {\n        try {\n            console.log('🔄 Ürün durumu güncelleniyor:', {\n                productId,\n                isApproved,\n                message\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_PRODUCT_STATUS, {\n                productId,\n                isApproved,\n                message\n            });\n            console.log('✅ Ürün durumu başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürün durumu güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün durumu güncellenirken bir hata oluştu'\n            };\n        }\n    },\n    // Ürün admin notunu getir\n    async getProductMessage (productId) {\n        try {\n            console.log('📝 Ürün admin notu alınıyor, productId:', productId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_PRODUCT_MESSAGE, \"/\").concat(productId));\n            console.log('✅ Ürün admin notu başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error('❌ Ürün admin notu alınırken hata:', error);\n            // 404 hatası ise kayıt yok demektir\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                return {\n                    success: true,\n                    data: null\n                };\n            }\n            // Diğer hatalar için false döndür\n            return {\n                success: false,\n                error: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün admin notu alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Tüm kategorileri getir (public)\n    async getCategories () {\n        try {\n            console.log('📂 Kategoriler alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CATEGORIES);\n            console.log('✅ Kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Kategoriye göre alt kategorileri getir (public)\n    async getSubCategoriesByCategory (categoryId) {\n        try {\n            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);\n            const response = await apiClient.get(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_SUBCATEGORIES_BY_CATEGORY.replace('{categoryId}', categoryId.toString())));\n            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Alt kategoriler alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Alt kategoriler alınırken bir hata oluştu',\n                data: []\n            };\n        }\n    },\n    // Ürünleri filtrele (public)\n    async filterProducts (filterRequest) {\n        try {\n            console.log('🔍 Ürünler filtreleniyor:', filterRequest);\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.FILTER_PRODUCTS, filterRequest);\n            console.log('✅ Ürünler başarıyla filtrelendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Ürünler filtrelenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürünler filtrelenirken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Reference data getir (public)\n    async getReferenceData () {\n        try {\n            console.log('📋 Reference data alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_REFERENCE_DATA);\n            console.log('✅ Reference data başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Reference data alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Reference data alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    }\n};\nconst cartService = {\n    // Sepete ürün ekle\n    async addToCart (productVariantId, quantity, isCustomerPrice) {\n        try {\n            console.log('🛒 Sepete ürün ekleniyor:', {\n                productVariantId,\n                quantity,\n                isCustomerPrice\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ADD_TO_CART, {\n                productVariantId,\n                quantity,\n                isCustomerPrice\n            });\n            console.log('✅ Ürün sepete başarıyla eklendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepete ürün eklenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün sepete eklenirken bir hata oluştu'\n            };\n        }\n    },\n    // Sepet içeriklerini getir\n    async getCartItems () {\n        try {\n            console.log('🛒 Sepet içerikleri alınıyor...');\n            const response = await apiClient.get(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.GET_CART_ITEMS);\n            console.log('✅ Sepet içerikleri başarıyla alındı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet içerikleri alınırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Sepet içerikleri alınırken bir hata oluştu',\n                data: null\n            };\n        }\n    },\n    // Sepetten ürün çıkar\n    async removeFromCart (productVariantId) {\n        try {\n            console.log('🗑️ Sepetten ürün çıkarılıyor:', {\n                productVariantId\n            });\n            const response = await apiClient.delete(\"\".concat(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.REMOVE_FROM_CART, \"/\").concat(productVariantId));\n            console.log('✅ Ürün sepetten başarıyla çıkarıldı:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepetten ürün çıkarılırken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün sepetten çıkarılırken bir hata oluştu'\n            };\n        }\n    },\n    // Sepet ürün miktarını güncelle\n    async updateCartQuantity (productVariantId, quantity) {\n        try {\n            console.log('🔄 Sepet ürün miktarı güncelleniyor:', {\n                productVariantId,\n                quantity\n            });\n            const response = await apiClient.post(_constants_apiEndpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.UPDATE_CART_QUANTITY, {\n                productVariantId,\n                quantity\n            });\n            console.log('✅ Sepet ürün miktarı başarıyla güncellendi:', response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('❌ Sepet ürün miktarı güncellenirken hata:', error);\n            return {\n                success: false,\n                error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ürün miktarı güncellenirken bir hata oluştu'\n            };\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

});