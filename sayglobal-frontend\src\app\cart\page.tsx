'use client';

import { useCartItems, useDiscountRate, useRemoveFromCart, useUpdateCartQuantity } from '@/hooks/useCart';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function CartPage() {
    // API'den sepet verilerini çek
    const { data: cartData, isLoading, error, refetch } = useCartItems();
    const { data: discountData } = useDiscountRate();
    const removeFromCartMutation = useRemoveFromCart();
    const updateQuantityMutation = useUpdateCartQuantity();

    // API'den gelen veriler
    const items = cartData?.items || [];
    const isCustomerPrice = cartData?.isCustomerPrice || false;
    const discountRate = discountData?.discountRate || 0;

    // Sepetten ürün çıkarma fonksiyonu
    const handleRemoveFromCart = async (productVariantId: number) => {
        try {
            await removeFromCartMutation.mutateAsync(productVariantId);
        } catch (error) {
            console.error('Sepetten ürün çıkarma hatası:', error);
        }
    };

    // Sepet ürün miktarını güncelleme fonksiyonu
    const handleUpdateQuantity = async (productVariantId: number, newQuantity: number) => {
        if (newQuantity <= 0) {
            // Miktar 0 veya negatifse ürünü sepetten çıkar
            await handleRemoveFromCart(productVariantId);
            return;
        }

        try {
            await updateQuantityMutation.mutateAsync({ productVariantId, quantity: newQuantity });
        } catch (error) {
            console.error('Sepet ürün miktarı güncelleme hatası:', error);
        }
    };

    // Toplam hesaplamaları
    const calculateTotals = () => {
        if (items.length === 0) {
            return {
                totalPrice: 0,
                totalPV: 0,
                totalCV: 0,
                totalSP: 0
            };
        }

        return items.reduce((totals, item) => {
            const quantity = item.quantity;
            let finalPrice = item.price;

            // İsCustomerPrice false ise indirimli fiyat hesapla
            if (!isCustomerPrice) {
                const extraDiscount = item.extraDiscount || 0;
                const totalDiscountRate = discountRate + extraDiscount;
                finalPrice = item.price * (1 - totalDiscountRate / 100);
            }

            // Puanları fiyat üzerinden hesapla (ratio olarak geliyorlar)
            const calculatedPV = finalPrice * (item.pv / 100);
            const calculatedCV = finalPrice * (item.cv / 100);
            const calculatedSP = finalPrice * (item.sp / 100);

            return {
                totalPrice: totals.totalPrice + (finalPrice * quantity),
                totalPV: totals.totalPV + (calculatedPV * quantity),
                totalCV: totals.totalCV + (calculatedCV * quantity),
                totalSP: totals.totalSP + (calculatedSP * quantity)
            };
        }, {
            totalPrice: 0,
            totalPV: 0,
            totalCV: 0,
            totalSP: 0
        });
    };

    const totals = calculateTotals();

    // Loading durumu
    if (isLoading) {
        return (
            <div className="container mx-auto px-4 py-16">
                <div className="text-center">
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex flex-col items-center"
                    >
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"></div>
                        <p className="text-gray-600">Sepetiniz yükleniyor...</p>
                    </motion.div>
                </div>
            </div>
        );
    }

    // Error durumu
    if (error) {
        return (
            <div className="container mx-auto px-4 py-16">
                <div className="text-center">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto"
                    >
                        <div className="text-red-600 mb-4">
                            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-red-800 mb-2">Sepet Yüklenemedi</h3>
                        <p className="text-red-600 mb-4">Sepetiniz yüklenirken bir hata oluştu.</p>
                        <button
                            onClick={() => refetch()}
                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Tekrar Dene
                        </button>
                    </motion.div>
                </div>
            </div>
        );
    }

    if (items.length === 0) {
        return (
            <div className="container mx-auto px-4 py-16">
                <div className="text-center">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-24 w-24 text-gray-400 mx-auto mb-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                            />
                        </svg>
                        <h1 className="text-3xl font-bold text-gray-800 mb-4">Sepetiniz Boş</h1>
                        <p className="text-gray-600 mb-8 max-w-md mx-auto">
                            Henüz sepetinizde ürün bulunmuyor. Alışverişe başlamak için ürünlerimizi keşfedin.
                        </p>
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Link
                                href="/products"
                                className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 inline-flex items-center space-x-2"
                            >
                                <span>Alışverişe Başla</span>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-5 w-5"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </Link>
                        </motion.div>
                    </motion.div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-16">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <div className="flex justify-between items-center mb-8">
                    <h1 className="text-3xl font-bold text-white">Sepetim ({items.length} ürün)</h1>
                    <motion.button
                        onClick={() => {
                            // API'den sepeti temizle - şimdilik sadece refresh yapalım
                            refetch();
                        }}
                        className="text-red-600 hover:text-red-700 font-medium flex items-center space-x-2"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                        </svg>
                        <span>Sepeti Temizle</span>
                    </motion.button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Ürün Listesi */}
                    <div className="lg:col-span-2 space-y-4">
                        {items.map((item, index) => {
                            let finalPrice = item.price;
                            let totalDiscountRate = 0;

                            // İsCustomerPrice false ise indirimli fiyat hesapla
                            if (!isCustomerPrice) {
                                const extraDiscount = item.extraDiscount || 0;
                                totalDiscountRate = discountRate + extraDiscount;
                                finalPrice = item.price * (1 - totalDiscountRate / 100);
                            }

                            // Puanları fiyat üzerinden hesapla (ratio olarak geliyorlar)
                            const calculatedPV = finalPrice * (item.pv / 100);
                            const calculatedCV = finalPrice * (item.cv / 100);
                            const calculatedSP = finalPrice * (item.sp / 100);

                            return (
                                <motion.div
                                    key={item.variantId}
                                    className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: index * 0.1, duration: 0.5 }}
                                >
                                    <div className="flex items-center space-x-4">
                                        <div className="relative w-20 h-20 flex-shrink-0">
                                            <Image
                                                src={item.mainImageUrl}
                                                alt={item.productName}
                                                fill
                                                className="object-cover rounded-lg"
                                            />
                                            {totalDiscountRate > 0 && (
                                                <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                                    %{totalDiscountRate.toFixed(0)}
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex-1">
                                            <h3 className="text-lg font-semibold text-gray-800">{item.productName}</h3>
                                            <p className="text-gray-600 text-sm">{item.brandName}</p>
                                            <div className="flex items-center space-x-2 mt-2">
                                                <span className="text-lg font-bold text-purple-700">
                                                    {finalPrice.toFixed(2)} ₺
                                                </span>
                                                {totalDiscountRate > 0 && (
                                                    <span className="text-sm text-gray-500 line-through">
                                                        {item.price.toFixed(2)} ₺
                                                    </span>
                                                )}
                                            </div>

                                            {/* Puan Badgeleri */}
                                            <div className="flex items-center space-x-2 mt-2">
                                                {calculatedPV > 0 && (
                                                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                                                        PV: {(calculatedPV * item.quantity).toFixed(0)}
                                                    </span>
                                                )}
                                                {calculatedCV > 0 && (
                                                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                                                        CV: {(calculatedCV * item.quantity).toFixed(0)}
                                                    </span>
                                                )}
                                                {calculatedSP > 0 && (
                                                    <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium">
                                                        SP: {(calculatedSP * item.quantity).toFixed(0)}
                                                    </span>
                                                )}
                                            </div>
                                        </div>

                                        <div className="flex items-center space-x-3">
                                            <div className="flex items-center border border-gray-300 rounded-lg">
                                                <motion.button
                                                    onClick={() => handleUpdateQuantity(item.variantId, item.quantity - 1)}
                                                    className="p-2 hover:bg-gray-100 transition-colors text-purple-800 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    whileTap={{ scale: 0.9 }}
                                                    disabled={item.quantity <= 1 || updateQuantityMutation.isPending}
                                                >
                                                    {updateQuantityMutation.isPending ? (
                                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                                                    ) : (
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            className="h-4 w-4"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                                        </svg>
                                                    )}
                                                </motion.button>
                                                <span className="px-4 py-2 text-gray-800 font-medium">{item.quantity}</span>
                                                <motion.button
                                                    onClick={() => handleUpdateQuantity(item.variantId, item.quantity + 1)}
                                                    className="p-2 hover:bg-gray-100 transition-colors text-purple-800 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    whileTap={{ scale: 0.9 }}
                                                    disabled={item.quantity >= item.stock || updateQuantityMutation.isPending}
                                                >
                                                    {updateQuantityMutation.isPending ? (
                                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                                                    ) : (
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            className="h-4 w-4"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                        </svg>
                                                    )}
                                                </motion.button>
                                            </div>

                                            <motion.button
                                                onClick={() => handleRemoveFromCart(item.variantId)}
                                                className="text-red-600 hover:text-red-700 p-2"
                                                whileHover={{ scale: 1.1 }}
                                                whileTap={{ scale: 0.9 }}
                                                disabled={removeFromCartMutation.isPending}
                                            >
                                                {removeFromCartMutation.isPending ? (
                                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"></div>
                                                ) : (
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        className="h-5 w-5"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke="currentColor"
                                                    >
                                                        <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            strokeWidth={2}
                                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                                        />
                                                    </svg>
                                                )}
                                            </motion.button>
                                        </div>
                                    </div>
                                </motion.div>
                            );
                        })}
                    </div>

                    {/* Sipariş Özeti */}
                    <div className="lg:col-span-1">
                        <motion.div
                            className="bg-white rounded-lg p-6 shadow-md sticky top-8"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3, duration: 0.6 }}
                        >
                            <h2 className="text-xl font-bold text-gray-800 mb-6">Sipariş Özeti</h2>

                            <div className="space-y-3 mb-6">
                                <div className="flex justify-between text-gray-600">
                                    <span>Ürün Toplamı:</span>
                                    <span>{totals.totalPrice.toFixed(2)} ₺</span>
                                </div>
                                <div className="flex justify-between text-gray-600">
                                    <span>Kargo:</span>
                                    <span className="text-green-600">Ücretsiz</span>
                                </div>

                                {/* Puan Toplamları */}
                                <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                                    <h3 className="text-sm font-medium text-gray-700 mb-2">Kazanacağınız Puanlar:</h3>
                                    <div className="grid grid-cols-3 gap-2">
                                        {totals.totalPV > 0 && (
                                            <div className="text-center">
                                                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium block">
                                                    PV: {totals.totalPV.toFixed(0)}
                                                </span>
                                            </div>
                                        )}
                                        {totals.totalCV > 0 && (
                                            <div className="text-center">
                                                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium block">
                                                    CV: {totals.totalCV.toFixed(0)}
                                                </span>
                                            </div>
                                        )}
                                        {totals.totalSP > 0 && (
                                            <div className="text-center">
                                                <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium block">
                                                    SP: {totals.totalSP.toFixed(0)}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <div className="border-t pt-3">
                                    <div className="flex justify-between text-lg font-bold text-gray-800">
                                        <span>Toplam:</span>
                                        <span className="text-purple-700">{totals.totalPrice.toFixed(2)} ₺</span>
                                    </div>
                                </div>
                            </div>

                            <Link href="/checkout">
                                <motion.button
                                    className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    Ödemeye Geç
                                </motion.button>
                            </Link>

                            <div className="mt-4 text-center">
                                <Link
                                    href="/products"
                                    className="text-purple-600 hover:text-purple-700 font-medium inline-flex items-center space-x-1"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-4 w-4"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                    </svg>
                                    <span>Alışverişe Devam Et</span>
                                </Link>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </motion.div>
        </div>
    );
} 