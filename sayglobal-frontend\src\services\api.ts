import axios from 'axios';
import axiosRetry from 'axios-retry';
import { API_ENDPOINTS } from '@/constants/apiEndpoints';
import { useNetworkStore } from '@/stores/networkStore';
import { AdminProduct } from '@/types';

// API Base URL - Development'te proxy kullan, production'da direkt API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.sayglobalweb.com';

// Cookie'den belirli bir değeri okuma utility fonksiyonu
const getCookieValue = (name: string): string | null => {
    if (typeof document === 'undefined') return null; // SSR kontrolü

    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
        const cookieValue = parts.pop()?.split(';').shift();
        return cookieValue || null;
    }
    return null;
};

// Axios instance oluştur - HTTP-only cookies için withCredentials: true
const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
    timeout: 30000, // 30 saniye timeout
    withCredentials: true, // HTTP-only cookies için
});

// Yeniden deneme mekanizmasını yapılandır
axiosRetry(apiClient, {
    retries: 3, // 3 kez yeniden dene
    retryCondition: (error) => {
        // Sadece ağ hatalarında veya sunucu tarafı geçici hatalarında yeniden dene.
        // 401/403 gibi client hatalarında yeniden deneme, çünkü bu auth akışını geciktirir.
        if (error.response?.status && error.response.status >= 400 && error.response.status < 500) {
            return false;
        }
        return axiosRetry.isNetworkError(error) || axiosRetry.isIdempotentRequestError(error);
    },
    retryDelay: (retryCount, error) => {
        console.warn(`[axios-retry] Request failed: ${error.message}. Retry attempt #${retryCount}...`);
        // Her denemede bekleme süresini artır (1s, 2s, 4s)
        return Math.pow(2, retryCount - 1) * 1000;
    },
});

// Refresh işlemi devam ediyor mu kontrolü
let isRefreshing = false;
let failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
        if (error) {
            reject(error);
        } else {
            resolve(token);
        }
    });

    failedQueue = [];
};

// Herkese açık ve engellenmemesi gereken endpoint'ler
const publicEndpoints = [
    API_ENDPOINTS.LOGIN,
    API_ENDPOINTS.REGISTER,
    API_ENDPOINTS.REFRESH_TOKEN,
    API_ENDPOINTS.LOGOUT,
    //API_ENDPOINTS.FORGOT_PASSWORD,
    // Eklenebilecek diğer public endpoint'ler...
];

// Request interceptor - Cookie'leri otomatik gönder (HttpOnly cookie'ler için)
apiClient.interceptors.request.use(
    (config) => {
        console.log('📡 API Request başlıyor:', config.method?.toUpperCase(), config.url);
        console.log('🍪 withCredentials:', config.withCredentials);

        // HttpOnly cookie'ler JavaScript ile okunamaz, bu normal bir durum
        // withCredentials: true olduğu için browser cookie'leri otomatik gönderir
        // Backend HttpOnly cookie'yi kontrol edecek

        // Eğer cookie JavaScript ile okunabiliyorsa Authorization header'ına da ekle
        const accessToken = getCookieValue('AccessToken');
        if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
            console.log('🔑 Authorization header eklendi (JS readable cookie)');
        } else {
            console.log('🔑 Cookie HttpOnly olabilir - browser otomatik gönderecek');
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor - Token yenileme ve hata yönetimi
apiClient.interceptors.response.use(
    (response) => {
        // Başarılı response'larda network status'u online'a çek
        const currentStatus = useNetworkStore.getState().status;
        if (currentStatus !== 'online') {
            console.log('✅ API başarılı - Network status online\'a çekiliyor');
            useNetworkStore.getState().setStatus('online');
        }
        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        // Login endpoint'inden gelen 401 hatasını token refresh döngüsünden çıkar
        // User/Me endpoint'ini bu kontrolden kaldırıyoruz ki token süresi dolduğunda yenileyebilsin.
        // REFRESH endpoint'ini de döngüden çıkarıyoruz ki sonsuz döngüye girmesin.
        if ((originalRequest.url?.includes(API_ENDPOINTS.LOGIN) ||
            originalRequest.url?.includes(API_ENDPOINTS.REFRESH_TOKEN)) &&
            error.response?.status === 401) {
            return Promise.reject(error);
        }

        // HİBRİT ÇÖZÜM - HttpOnly Cookie'ler için Güncellenmiş Yaklaşım:
        // HttpOnly cookie'ler JavaScript ile okunamaz, bu yüzden cookie varlığını kontrol edemeyiz.
        // Bunun yerine, token yenileme isteğini gönderip sonucuna göre karar vereceğiz.
        // Eğer refresh token yoksa, backend 401 döndürecek ve biz bunu yakalayacağız.

        // 1. ÖNCELİK: Token yenileme mantığı
        // 401 durumunda token yenileme - retry flag kontrolü ile döngüyü engelle
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true; // Bu request'in retry edildiğini işaretle

            if (isRefreshing) {
                // Eğer refresh işlemi devam ediyorsa, kuyruğa ekle
                return new Promise((resolve, reject) => {
                    failedQueue.push({ resolve, reject });
                }).then(() => {
                    // Refresh tamamlandıktan sonra orijinal isteği tekrar gönder
                    return apiClient(originalRequest);
                }).catch(err => {
                    return Promise.reject(err);
                });
            }

            isRefreshing = true;

            // Refresh token için özel retry mekanizması
            const attemptRefresh = async (retryCount = 0): Promise<any> => {
                try {
                    const refreshResponse = await apiClient.get(API_ENDPOINTS.REFRESH_TOKEN);
                    return refreshResponse;
                } catch (refreshError: any) {
                    // Retry koşulları:
                    // 1. Ağ hatası (timeout, connection error vb.)
                    // 2. 401 hatası ama henüz max retry'a ulaşmadıysak (timeout nedeniyle 401 olabilir)
                    const isNetworkError = axios.isAxiosError(refreshError) && !refreshError.response;
                    const is401Error = axios.isAxiosError(refreshError) && refreshError.response?.status === 401;
                    const shouldRetry = (isNetworkError || is401Error) && retryCount < 2;

                    if (shouldRetry) {
                        console.log(`🔄 Refresh token denemesi ${retryCount + 1}/3 başarısız (${isNetworkError ? 'Ağ hatası' : '401 - Timeout olabilir'}). ${retryCount < 1 ? 'Tekrar deneniyor...' : 'Son deneme yapılıyor...'}`);

                        // Exponential backoff: 1s, 2s, 4s
                        const delay = Math.pow(2, retryCount) * 1000;
                        await new Promise(resolve => setTimeout(resolve, delay));

                        return attemptRefresh(retryCount + 1);
                    }

                    // Max retry'a ulaştık veya kesin bir hata aldık
                    throw refreshError;
                }
            };

            try {
                // Refresh token çağrısı - retry mekanizması ile
                const refreshResponse = await attemptRefresh();

                // Tüm bekleyen istekleri başarılı olarak işaretle
                processQueue(null);
                isRefreshing = false;

                // Başarılı refresh sonrası orijinal isteği tekrar gönder
                return apiClient(originalRequest);

            } catch (refreshError: any) {
                // ÖNEMLİ: isRefreshing bayrağını mutlaka sıfırla
                isRefreshing = false;

                // 3 deneme sonrasında da 401 alıyorsak, bu gerçekten kullanıcının giriş yapmadığı anlamına gelir
                if (axios.isAxiosError(refreshError) && refreshError.response?.status === 401) {
                    console.log('🚪 3 deneme sonrasında da 401 hatası. Kullanıcı gerçekten giriş yapmamış.');

                    // Tüm bekleyen istekleri orijinal hata ile reddet
                    processQueue(error, null);

                    // Orijinal hatayı döndür ki uygulama "giriş yapmamış" durumuna geçsin
                    return Promise.reject(error);
                }

                // Diğer hatalar için normal işlem
                processQueue(refreshError, null);

                if (axios.isAxiosError(refreshError) && !refreshError.response) {
                    // 3 deneme sonrasında hala ağ hatası alıyorsak, bu ciddi bir bağlantı sorunu
                    console.log('🔌 3 deneme sonrasında refresh token yenilenemedi. Ağ bağlantısı sorunlu.');
                    useNetworkStore.getState().setStatus('reconnecting');
                } else {
                    console.log('💥 Beklenmedik hata sonrası auth:force-logout event gönderiliyor');
                    window.dispatchEvent(new CustomEvent('auth:force-logout'));
                }

                return Promise.reject(refreshError);
            }
        }

        // 2. ÖNCELİK: Genel ağ hatalarını yakalama
        // Eğer hata 401 değilse ve bir ağ hatasıysa (sunucudan yanıt yoksa),
        // bu genel bir internet kesintisidir.
        if (axios.isAxiosError(error) && !error.response) {
            console.log('🔌 Genel ağ hatası algılandı. Yeniden bağlanma moduna geçiliyor.');
            useNetworkStore.getState().setStatus('reconnecting');
            // Burada hatayı yutup banner'ın çalışmasına izin veriyoruz.
            // Component'in tekrar denemesi için hatayı reject etmiyoruz ki sürekli error state'i göstermesin.
            // Bunun yerine, bir daha asla çözülmeyecek bir promise döndürerek isteği askıda bırakıyoruz.
            return new Promise(() => { });
        }

        // Diğer tüm hatalar (500, 404, 403 vb.) normal şekilde componente geri dönsün.
        return Promise.reject(error);
    }
);

export default apiClient;

// ===========================================
// ADDRESS SERVICES
// ===========================================

export const addressService = {
    // Kullanıcının adreslerini listele
    async getAddresses(): Promise<any> {
        try {
            console.log('📍 Adresler alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.USER_ADDRESSES);
            console.log('📍 API Response:', response);
            console.log('📍 Response data:', response.data);
            console.log('📍 Response status:', response.status);
            console.log('📍 Response data type:', typeof response.data);
            console.log('📍 Response data is array:', Array.isArray(response.data));

            // Eğer response.data bir object ise, içindeki property'leri kontrol et
            if (typeof response.data === 'object' && response.data !== null) {
                console.log('📍 Response data keys:', Object.keys(response.data));
                console.log('📍 Response data values:', Object.values(response.data));

                // Muhtemel nested yapıları kontrol et
                if (response.data.data) {
                    console.log('📍 Nested data found:', response.data.data);
                    console.log('📍 Nested data is array:', Array.isArray(response.data.data));
                }
                if (response.data.addresses) {
                    console.log('📍 Addresses property found:', response.data.addresses);
                    console.log('📍 Addresses is array:', Array.isArray(response.data.addresses));
                }
                if (response.data.result) {
                    console.log('📍 Result property found:', response.data.result);
                    console.log('📍 Result is array:', Array.isArray(response.data.result));
                }
            }

            // Farklı response yapılarını dene
            let addressData = response.data;

            // Eğer response.data.data varsa ve array ise onu kullan
            if (response.data.data && Array.isArray(response.data.data)) {
                addressData = response.data.data;
                console.log('📍 Using nested data array');
            }
            // Eğer response.data.addresses varsa ve array ise onu kullan
            else if (response.data.addresses && Array.isArray(response.data.addresses)) {
                addressData = response.data.addresses;
                console.log('📍 Using addresses property');
            }
            // Eğer response.data.result varsa ve array ise onu kullan
            else if (response.data.result && Array.isArray(response.data.result)) {
                addressData = response.data.result;
                console.log('📍 Using result property');
            }
            // Eğer response.data direkt array ise onu kullan
            else if (Array.isArray(response.data)) {
                addressData = response.data;
                console.log('📍 Using direct response data');
            }
            // Eğer hiçbiri değilse boş array
            else {
                console.warn('📍 No valid array found in response, using empty array');
                addressData = [];
            }

            console.log('📍 Final address data:', addressData);
            console.log('📍 Final address data type:', typeof addressData);
            console.log('📍 Final address data is array:', Array.isArray(addressData));
            console.log('📍 Final address count:', addressData.length);

            return {
                success: true,
                data: addressData
            };
        } catch (error: any) {
            console.error('❌ Adresler alınırken hata:', error);
            console.error('❌ Error response:', error.response);
            console.error('❌ Error data:', error.response?.data);

            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Adresler alınırken bir hata oluştu',
                data: [] // Hata durumunda boş array döndür
            };
        }
    },

    // Yeni adres ekle
    async createAddress(addressData: any): Promise<any> {
        try {
            console.log('➕ Yeni adres oluşturuluyor:', addressData);
            const response = await apiClient.post(API_ENDPOINTS.CREATE_ADDRESS, addressData);
            console.log('✅ Adres oluşturma başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Adres eklenirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Adres eklenirken bir hata oluştu'
            };
        }
    },

    // Adres sil
    async deleteAddress(addressId: number, userId: number): Promise<any> {
        try {
            console.log('🗑️ Adres siliniyor:', { addressId, userId });
            const response = await apiClient.post(API_ENDPOINTS.DELETE_ADDRESS, { addressId, userId });
            console.log('✅ Adres silme başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Adres silinirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Adres silinirken bir hata oluştu'
            };
        }
    },

    // Varsayılan adres ayarla
    async setDefaultAddress(addressId: number): Promise<any> {
        try {
            console.log('⭐ Varsayılan adres ayarlanıyor:', { addressId });
            const response = await apiClient.post(`${API_ENDPOINTS.SET_DEFAULT_ADDRESS}?addressId=${addressId}`);
            console.log('✅ Varsayılan adres ayarlama başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Varsayılan adres ayarlanırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Varsayılan adres ayarlanırken bir hata oluştu'
            };
        }
    }
};

// ===========================================
// USER SERVICES
// ===========================================

export const userService = {
    // Kullanıcı profilini güncelle
    async updateProfile(profileData: {
        firstName?: string;
        lastName?: string;
        phoneNumber?: string;
        dateOfBirth?: string;
        gender?: number;
        location?: string;
    }): Promise<any> {
        try {
            console.log('👤 Profil güncelleniyor:', profileData);
            const response = await apiClient.post(API_ENDPOINTS.UPDATE_PROFILE, profileData);
            console.log('✅ Profil güncelleme başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Profil güncellenirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Profil güncellenirken bir hata oluştu'
            };
        }
    },

    // Admin kullanıcıları getir (filtreleme ve sayfalama ile)
    async getUsers(params: {
        page?: number;
        pageSize?: number;
        search?: string;
        roleId?: number;
        isActive?: boolean;
    }): Promise<any> {
        try {
            // Request body oluştur
            const requestBody: any = {
                page: params.page || 1, // Backend 1-based indexing kullanıyor
                pageSize: params.pageSize || 10,
                search: params.search || ""
            };

            // Sadece roleId 0'dan farklıysa ekle
            if (params.roleId && params.roleId > 0) {
                requestBody.roleId = params.roleId;
            }

            // Sadece isActive parametresi gönderildiyse ekle
            if (params.isActive !== undefined) {
                requestBody.isActive = params.isActive;
            }

            const response = await apiClient.post(API_ENDPOINTS.GET_USERS, requestBody);

            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Kullanıcılar alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Kullanıcılar alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Kullanıcı rol sayılarını getir
    async getUserRoleCounts(): Promise<any> {
        try {
            const response = await apiClient.get(API_ENDPOINTS.GET_USER_ROLE_COUNTS);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Kullanıcı rol sayıları alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Kullanıcı rol sayıları alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Kullanıcı indirim oranını getir
    async getDiscountRate(): Promise<any> {
        try {
            console.log('💰 Kullanıcı indirim oranı alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_DISCOUNT_RATE);
            console.log('✅ Kullanıcı indirim oranı başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Kullanıcı indirim oranı alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Kullanıcı indirim oranı alınırken bir hata oluştu',
                data: null
            };
        }
    }
};

// ===========================================
// PRODUCT SERVICES
// ===========================================

export const productService = {
    // Markaları getir
    async getBrands(): Promise<any> {
        try {
            console.log('🏷️ Markalar alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_BRANDS);
            console.log('✅ Markalar başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Markalar alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Markalar alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Markaya göre kategorileri getir
    async getCategoriesByBrand(brandId: number): Promise<any> {
        try {
            console.log('📂 Kategoriler alınıyor, brandId:', brandId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_CATEGORIES_BY_BRAND}/${brandId}`);
            console.log('✅ Kategoriler başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Kategoriler alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Kategoriler alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Kategoriye göre alt kategorileri getir
    async getSubCategories(categoryId: number): Promise<any> {
        try {
            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_SUBCATEGORIES}/${categoryId}/subcategories`);
            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Alt kategoriler alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Alt kategoriler alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Alt kategori özelliklerini getir
    async getSubCategoryFeatures(subCategoryId: number): Promise<any> {
        try {
            console.log('🔧 Alt kategori özellikleri alınıyor, subCategoryId:', subCategoryId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_SUBCATEGORY_FEATURES}/${subCategoryId}`);
            console.log('✅ Alt kategori özellikleri başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Alt kategori özellikleri alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Alt kategori özellikleri alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Özellik değerlerini getir
    async getFeatureValues(definitionId: number): Promise<any> {
        try {
            console.log('🏷️ Özellik değerleri alınıyor, definitionId:', definitionId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_FEATURE_VALUES}/${definitionId}`);
            console.log('✅ Özellik değerleri başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Özellik değerleri alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Özellik değerleri alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Tüm özellik tanımlarını getir
    async getAllFeatureDefinitions(): Promise<any> {
        try {
            console.log('🔍 Tüm özellik tanımları alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_ALL_FEATURE_DEFINITIONS);
            console.log('✅ Tüm özellik tanımları başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Tüm özellik tanımları alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Tüm özellik tanımları alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Yeni ürün ekle (tam ürün - varyantlarla birlikte)
    async createFullProduct(productData: any): Promise<any> {
        try {
            console.log('➕ Tam ürün oluşturuluyor:', productData);
            const response = await apiClient.post(API_ENDPOINTS.CREATE_FULL_PRODUCT, productData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            console.log('✅ Tam ürün oluşturma başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Tam ürün oluşturulurken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün oluşturulurken bir hata oluştu'
            };
        }
    },

    // Satıcı ürünü ekle (dealership product - PV/CV/SP olmadan)
    async createDealershipProduct(productData: any): Promise<any> {
        try {
            console.log('➕ Satıcı ürünü oluşturuluyor:', productData);
            const response = await apiClient.post(API_ENDPOINTS.CREATE_DEALERSHIP_PRODUCT, productData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            console.log('✅ Satıcı ürünü oluşturma başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Satıcı ürünü oluşturulurken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün oluşturulurken bir hata oluştu'
            };
        }
    },

    // Ürün görseli ekle
    async addProductImage(imageData: any): Promise<any> {
        try {
            console.log('🖼️ Ürün görseli ekleniyor:', imageData);
            const response = await apiClient.post(API_ENDPOINTS.ADD_PRODUCT_IMAGE, imageData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            console.log('✅ Ürün görseli ekleme başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürün görseli eklenirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün görseli eklenirken bir hata oluştu'
            };
        }
    },

    // Ürün görselini sil
    async deleteProductImage(imageId: number): Promise<any> {
        try {
            console.log('🗑️ Ürün görseli siliniyor, imageId:', imageId);
            const response = await apiClient.delete(`${API_ENDPOINTS.DELETE_PRODUCT_IMAGE}/${imageId}`);
            console.log('✅ Ürün görseli silme başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürün görseli silinirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün görseli silinirken bir hata oluştu'
            };
        }
    },

    // Ürün görselini değiştir
    async replaceProductImage(imageId: number, newImageFile: File): Promise<any> {
        try {
            console.log('🔄 Ürün görseli değiştiriliyor, imageId:', imageId);
            const formData = new FormData();
            formData.append('file', newImageFile);

            const response = await apiClient.put(`${API_ENDPOINTS.REPLACE_PRODUCT_IMAGE}/${imageId}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            console.log('✅ Ürün görseli değiştirme başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürün görseli değiştirilirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün görseli değiştirilirken bir hata oluştu'
            };
        }
    },

    // Admin ürünlerini getir
    async getAdminProducts(params: { pageNumber?: number; pageSize?: number; productName?: string }): Promise<AdminProduct[]> {
        try {
            const response = await apiClient.get(API_ENDPOINTS.GET_ADMIN_PRODUCTS, { params });
            return Array.isArray(response.data) ? response.data : [];
        } catch (error) {
            console.error('❌ Admin ürünleri alınırken hata:', error);
            return [];
        }
    },

    // Kullanıcıya ait ürünleri getir
    async getMyProducts(params: { page: number; pageSize: number; status?: number; name?: string }): Promise<any> {
        try {
            console.log('📦 Kullanıcıya ait ürünler alınıyor:', params);
            const response = await apiClient.post(API_ENDPOINTS.GET_MY_PRODUCTS, params);
            console.log('✅ Kullanıcıya ait ürünler başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Kullanıcıya ait ürünler alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürünler alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Dealership ürün detayını getir
    async getDealershipProductDetail(productId: number): Promise<any> {
        try {
            console.log('📦 Dealership ürün detayı alınıyor, productId:', productId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_DEALERSHIP_PRODUCT_DETAIL}/${productId}`);
            console.log('✅ Dealership ürün detayı başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Dealership ürün detayı alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün detayı alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Ürünü sil
    async deleteProduct(productId: number): Promise<any> {
        try {
            console.log('🗑️ Ürün siliniyor, productId:', productId);
            // API GET metodu ve query parametresi beklediği için ona uygun istek atıyoruz.
            const response = await apiClient.get(API_ENDPOINTS.DELETE_PRODUCT, {
                params: { productId }
            });
            console.log('✅ Ürün silme başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürün silinirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün silinirken bir hata oluştu'
            };
        }
    },

    // Ürünü güncelle (tam ürün - varyantlarla birlikte)
    async updateFullProduct(productData: any): Promise<any> {
        try {
            console.log('🔄 Tam ürün güncelleniyor:', productData);

            // FormData içeriğini detaylı logla
            console.log('📋 FormData içeriği:');
            for (let [key, value] of productData.entries()) {
                console.log(`${key}:`, value);
            }

            const response = await apiClient.post(API_ENDPOINTS.UPDATE_FULL_PRODUCT, productData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            console.log('✅ Tam ürün güncelleme başarılı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Tam ürün güncellenirken hata:', error);
            console.error('❌ Hata detayları:', error.response?.data);
            console.error('❌ HTTP Status:', error.response?.status);

            // Hata mesajını daha detaylı döndür
            const errorMessage = error.response?.data?.message ||
                error.response?.data?.title ||
                error.message ||
                'Ürün güncellenirken bir hata oluştu';

            throw new Error(errorMessage);
        }
    },

    // Admin ürün istatistiklerini getir
    async getAdminProductStatistics(): Promise<any> {
        try {
            console.log('📊 Admin ürün istatistikleri alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_ADMIN_PRODUCT_STATISTICS);
            console.log('✅ Admin ürün istatistikleri başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Admin ürün istatistikleri alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'İstatistikler alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Kullanıcı ürün istatistiklerini getir
    async getMyProductStatistics(): Promise<any> {
        try {
            console.log('📊 Kullanıcı ürün istatistikleri alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_MY_PRODUCT_STATISTICS);
            console.log('✅ Kullanıcı ürün istatistikleri başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data.data // API response'u data wrapper'ı içinde geliyor
            };
        } catch (error: any) {
            console.error('❌ Kullanıcı ürün istatistikleri alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'İstatistikler alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Basit ürün güncelleme (dealership için)
    async updateSimpleProduct(productFormData: FormData): Promise<any> {
        console.log('🔄 API Service: Basit ürün güncelleniyor...');
        console.log('🔗 Endpoint:', API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT);

        // FormData içeriğini logla
        console.log('📋 API Service: FormData contents:');
        for (let [key, value] of productFormData.entries()) {
            if (value instanceof File) {
                console.log(`  ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        }

        const response = await apiClient.post(API_ENDPOINTS.UPDATE_SIMPLE_PRODUCT, productFormData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        console.log('✅ API Service: Basit ürün başarıyla güncellendi');
        console.log('📄 Response status:', response.status);
        console.log('📄 Response data:', response.data);

        return response.data;
    },

    // Ürün detayını getir
    async getProductDetail(productId: number): Promise<any> {
        try {
            console.log('📦 Ürün detayı alınıyor, productId:', productId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_PRODUCT_DETAIL}/${productId}`);
            console.log('✅ Ürün detayı başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürün detayı alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün detayı alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Catalog ürün detayını getir (public)
    async getCatalogProductDetail(productId: number): Promise<any> {
        try {
            console.log('📦 Catalog ürün detayı alınıyor, productId:', productId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_CATALOG_PRODUCT_DETAIL}/${productId}`);
            console.log('✅ Catalog ürün detayı başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Catalog ürün detayı alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün detayı alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Ürün durumunu güncelle (onay/red)
    async updateProductStatus(productId: number, isApproved: boolean, message: string): Promise<any> {
        try {
            console.log('🔄 Ürün durumu güncelleniyor:', { productId, isApproved, message });
            const response = await apiClient.post(API_ENDPOINTS.UPDATE_PRODUCT_STATUS, {
                productId,
                isApproved,
                message
            });
            console.log('✅ Ürün durumu başarıyla güncellendi:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürün durumu güncellenirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün durumu güncellenirken bir hata oluştu'
            };
        }
    },

    // Ürün admin notunu getir
    async getProductMessage(productId: number): Promise<any> {
        try {
            console.log('📝 Ürün admin notu alınıyor, productId:', productId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_PRODUCT_MESSAGE}/${productId}`);
            console.log('✅ Ürün admin notu başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürün admin notu alınırken hata:', error);

            // 404 hatası ise kayıt yok demektir
            if (error.response?.status === 404) {
                return {
                    success: true,
                    data: null
                };
            }

            // Diğer hatalar için false döndür
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün admin notu alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Tüm kategorileri getir (public)
    async getCategories(): Promise<any> {
        try {
            console.log('📂 Kategoriler alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_CATEGORIES);
            console.log('✅ Kategoriler başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Kategoriler alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Kategoriler alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Kategoriye göre alt kategorileri getir (public)
    async getSubCategoriesByCategory(categoryId: number): Promise<any> {
        try {
            console.log('📁 Alt kategoriler alınıyor, categoryId:', categoryId);
            const response = await apiClient.get(`${API_ENDPOINTS.GET_SUBCATEGORIES_BY_CATEGORY.replace('{categoryId}', categoryId.toString())}`);
            console.log('✅ Alt kategoriler başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Alt kategoriler alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Alt kategoriler alınırken bir hata oluştu',
                data: []
            };
        }
    },

    // Ürünleri filtrele (public)
    async filterProducts(filterRequest: any): Promise<any> {
        try {
            console.log('🔍 Ürünler filtreleniyor:', filterRequest);
            const response = await apiClient.post(API_ENDPOINTS.FILTER_PRODUCTS, filterRequest);
            console.log('✅ Ürünler başarıyla filtrelendi:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Ürünler filtrelenirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürünler filtrelenirken bir hata oluştu',
                data: null
            };
        }
    },

    // Reference data getir (public)
    async getReferenceData(): Promise<any> {
        try {
            console.log('📋 Reference data alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_REFERENCE_DATA);
            console.log('✅ Reference data başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Reference data alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Reference data alınırken bir hata oluştu',
                data: null
            };
        }
    },
};

export const cartService = {
    // Sepete ürün ekle
    async addToCart(productVariantId: number, quantity: number, isCustomerPrice: boolean): Promise<any> {
        try {
            console.log('🛒 Sepete ürün ekleniyor:', { productVariantId, quantity, isCustomerPrice });
            const response = await apiClient.post(API_ENDPOINTS.ADD_TO_CART, {
                productVariantId,
                quantity,
                isCustomerPrice
            });
            console.log('✅ Ürün sepete başarıyla eklendi:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Sepete ürün eklenirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün sepete eklenirken bir hata oluştu'
            };
        }
    },

    // Sepet içeriklerini getir
    async getCartItems(): Promise<any> {
        try {
            console.log('🛒 Sepet içerikleri alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.GET_CART_ITEMS);
            console.log('✅ Sepet içerikleri başarıyla alındı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Sepet içerikleri alınırken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Sepet içerikleri alınırken bir hata oluştu',
                data: null
            };
        }
    },

    // Sepetten ürün çıkar
    async removeFromCart(productVariantId: number): Promise<any> {
        try {
            console.log('🗑️ Sepetten ürün çıkarılıyor:', { productVariantId });
            const url = `${API_ENDPOINTS.REMOVE_FROM_CART}/${productVariantId}`;
            console.log('🔍 API URL:', url);
            console.log('🔍 API_ENDPOINTS.REMOVE_FROM_CART:', API_ENDPOINTS.REMOVE_FROM_CART);
            const response = await apiClient.delete(url);
            console.log('✅ Ürün sepetten başarıyla çıkarıldı:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Sepetten ürün çıkarılırken hata:', error);
            console.error('❌ Error response:', error.response);
            console.error('❌ Error status:', error.response?.status);
            console.error('❌ Error data:', error.response?.data);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün sepetten çıkarılırken bir hata oluştu'
            };
        }
    },

    // Sepet ürün miktarını güncelle
    async updateCartQuantity(productVariantId: number, quantity: number): Promise<any> {
        try {
            console.log('🔄 Sepet ürün miktarı güncelleniyor:', { productVariantId, quantity });
            const response = await apiClient.post(API_ENDPOINTS.UPDATE_CART_QUANTITY, {
                productVariantId,
                quantity
            });
            console.log('✅ Sepet ürün miktarı başarıyla güncellendi:', response.data);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error('❌ Sepet ürün miktarı güncellenirken hata:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Ürün miktarı güncellenirken bir hata oluştu'
            };
        }
    },
};